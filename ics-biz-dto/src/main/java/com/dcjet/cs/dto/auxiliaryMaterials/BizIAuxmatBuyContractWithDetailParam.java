package com.dcjet.cs.dto.auxiliaryMaterials;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 包含表头和表体数据的参数类
 * <AUTHOR>
 * @date: 2025-01-15
 */
@Setter @Getter
@ApiModel(value = "辅料采购合同表头和表体传入参数")
public class BizIAuxmatBuyContractWithDetailParam implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;
    
    /**
     * 业务类型
     */
    @NotEmpty(message="业务类型不能为空！")
    @XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;
    
    /**
     * 购销合同号
     */
    @NotEmpty(message="购销合同号不能为空！")
    @XdoSize(max = 60, message = "购销合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("购销合同号")
    private String contractNo;
    
    /**
     * 购销年份
     */
    @NotNull(message="购销年份不能为空！")
    @ApiModelProperty("购销年份")
    private String contractYear;
    
    /**
     * 业务区分
     */
    @NotEmpty(message="业务区分不能为空！")
    @XdoSize(max = 20, message = "业务区分长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务区分")
    private String businessDistinction;
    
    /**
     * 备注
     */
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remark;
    
    /**
     * 版本号
     */
    @NotEmpty(message="版本号不能为空！")
    @XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;
    
    /**
     * 单据状态
     */
    @NotEmpty(message="单据状态不能为空！")
    @ApiModelProperty("单据状态")
    private String status;
    
    /**
     * 确认时间
     */
    @ApiModelProperty("确认时间")
    private Date confirmTime;
    
    /**
     * 审批状态
     */
    @XdoSize(max = 10, message = "审批状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("审批状态")
    private String apprStatus;
    
    /**
     * 创建人
     */
    @XdoSize(max = 10, message = "创建人长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String createBy;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    private String createTimeFrom;
    
    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    private String createTimeTo;
    
    /**
     * 最后修改人
     */
    @XdoSize(max = 10, message = "最后修改人长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("最后修改人")
    private String updateBy;
    
    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间")
    private Date updateTime;
    
    /**
     * 创建人部门编码
     */
    @XdoSize(max = 36, message = "创建人部门编码长度不能超过36位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人部门编码")
    private String sysOrgCode;
    
    /**
     * 企业编码
     */
    @XdoSize(max = 10, message = "企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 人民币划款金额
     */
    @ApiModelProperty("人民币划款金额")
    private BigDecimal paymentAmount;
    /**
     * 合同金额
     */
    @ApiModelProperty("合同金额")
    private BigDecimal contractAmount;

    /**
     * 汇率
     */
    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    /**
     * 关税税率
     */
    @ApiModelProperty("关税税率")
    private BigDecimal tariffRate;

    /**
     * 关税金额
     */
    @ApiModelProperty("关税金额")
    private BigDecimal tariffAmount;

    /**
     * 消费税率
     */
    @ApiModelProperty("消费税率")
    private BigDecimal consumptionTaxRate;

    /**
     * 消费税金额
     */
    @ApiModelProperty("消费税金额")
    private BigDecimal consumptionTaxAmount;

    /**
     * 增值税税率
     */
    @ApiModelProperty("增值税税率")
    private BigDecimal vatRate;

    /**
     * 增值税金额
     */
    @ApiModelProperty("增值税金额")
    private BigDecimal vatAmount;

    /**
     * 进出口代理费率
     */
    @ApiModelProperty("进出口代理费率")
    private BigDecimal importExportAgencyRate;

    /**
     * 进出口代理费
     */
    @ApiModelProperty("进出口代理费")
    private BigDecimal importExportAgencyFee;

    /**
     * 总部代理费率
     */
    @ApiModelProperty("总部代理费率")
    private BigDecimal headquartersAgencyRate;

    /**
     * 总部代理费
     */
    @ApiModelProperty("总部代理费")
    private BigDecimal headquartersAgencyFee;

    /**
     * 合同数量
     */
    @ApiModelProperty("合同数量")
    private BigDecimal contractQuantity;

    /**
     * 计费重量
     */
    @ApiModelProperty("计费重量")
    private BigDecimal billingWeight;

    /**
     * 清关费
     */
    @ApiModelProperty("清关费")
    private BigDecimal customsClearanceFee;

    /**
     * 集装箱检验费
     */
    @ApiModelProperty("集装箱检验费")
    private BigDecimal containerInspectionFee;

    /**
     * 货运代理费
     */
    @ApiModelProperty("货运代理费")
    private BigDecimal freightForwarderFee;

    /**
     * 保险费率
     */
    @ApiModelProperty("保险费率")
    private BigDecimal insuranceRate;

    /**
     * 保险费
     */
    @ApiModelProperty("保险费")
    private BigDecimal insuranceFee;
    /**
     * 是否划款通知保存过
     */
    @ApiModelProperty("是否划款通知保存过")
    private String isTransferNotice;
    
    /**
     * 表体数据列表
     */
    @ApiModelProperty("表体数据列表")
    private List<BizIAuxmatBuyContractListParam> details;
}
